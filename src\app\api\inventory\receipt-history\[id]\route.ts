import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/auth';

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/receipt-history/[id] - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/receipt-history/[id] - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("[API] GET /api/inventory/receipt-history/[id] - Start");

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view receipt details
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const receiptId = params.id;

    // Fetch detailed receipt information
    const receipt = await prisma.purchaseOrderReceiving.findUnique({
      where: { id: receiptId },
      include: {
        purchaseOrder: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true,
                phone: true,
                email: true,
                address: true,
              },
            },
            createdBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            approvedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            items: {
              include: {
                product: {
                  include: {
                    category: {
                      select: {
                        id: true,
                        name: true,
                      },
                    },
                    unit: {
                      select: {
                        id: true,
                        name: true,
                        abbreviation: true,
                      },
                    },
                  },
                },
                productSupplier: {
                  include: {
                    supplier: {
                      select: {
                        id: true,
                        name: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        receivedBy: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        items: {
          include: {
            purchaseOrderItem: {
              include: {
                product: {
                  include: {
                    category: {
                      select: {
                        id: true,
                        name: true,
                      },
                    },
                    unit: {
                      select: {
                        id: true,
                        name: true,
                        abbreviation: true,
                      },
                    },
                  },
                },
                productSupplier: {
                  include: {
                    supplier: {
                      select: {
                        id: true,
                        name: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!receipt) {
      return NextResponse.json({ error: 'Receipt not found' }, { status: 404 });
    }

    // Fetch related stock batches created during this receipt
    const relatedBatches = await prisma.stockBatch.findMany({
      where: {
        purchaseOrderId: receipt.purchaseOrderId,
        receivedDate: {
          gte: new Date(receipt.receivedAt.getTime() - 60000), // 1 minute before
          lte: new Date(receipt.receivedAt.getTime() + 60000), // 1 minute after
        },
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            unit: {
              select: {
                name: true,
                abbreviation: true,
              },
            },
          },
        },
        productSupplier: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Fetch related stock history entries
    const relatedStockHistory = await prisma.stockHistory.findMany({
      where: {
        referenceId: receipt.id,
        referenceType: 'PurchaseOrderReceiving',
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            unit: {
              select: {
                name: true,
                abbreviation: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        date: 'asc',
      },
    });

    // Fetch related activity logs
    const relatedActivityLogs = await prisma.activityLog.findMany({
      where: {
        OR: [
          {
            action: 'RECEIVE_PURCHASE_ORDER',
            details: {
              contains: receipt.purchaseOrder.id,
            },
          },
          {
            action: 'CREATE_STOCK_BATCH',
            details: {
              contains: receipt.purchaseOrder.id,
            },
          },
        ],
        timestamp: {
          gte: new Date(receipt.receivedAt.getTime() - 300000), // 5 minutes before
          lte: new Date(receipt.receivedAt.getTime() + 300000), // 5 minutes after
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        timestamp: 'asc',
      },
    });

    // Calculate comprehensive metrics
    const metrics = {
      totalItemsOrdered: receipt.purchaseOrder.items.reduce(
        (sum, item) => sum + Number(item.quantity),
        0
      ),
      totalItemsReceived: receipt.items.reduce(
        (sum, item) => sum + Number(item.receivedQuantity),
        0
      ),
      totalDiscrepancies: receipt.items.reduce(
        (sum, item) => sum + Math.abs(Number(item.discrepancyQuantity)),
        0
      ),
      totalValue: receipt.items.reduce(
        (sum, item) => sum + (Number(item.receivedQuantity) * Number(item.purchaseOrderItem.unitPrice)),
        0
      ),
      batchesCreated: relatedBatches.length,
      uniqueProducts: new Set(receipt.items.map(item => item.purchaseOrderItem.productId)).size,
      fulfillmentPercentage: receipt.purchaseOrder.items.length > 0 
        ? Math.round((receipt.items.length / receipt.purchaseOrder.items.length) * 100)
        : 0,
      hasDiscrepancies: receipt.items.some(item => Number(item.discrepancyQuantity) !== 0),
      processingTime: receipt.createdAt.getTime() - receipt.receivedAt.getTime(),
    };

    // Enrich receipt data with additional information
    const enrichedReceipt = {
      ...receipt,
      metrics,
      relatedBatches,
      relatedStockHistory,
      relatedActivityLogs,
      auditTrail: {
        created: {
          at: receipt.receivedAt,
          by: receipt.receivedBy,
        },
        purchaseOrder: {
          created: {
            at: receipt.purchaseOrder.createdAt,
            by: receipt.purchaseOrder.createdBy,
          },
          approved: receipt.purchaseOrder.approvedAt ? {
            at: receipt.purchaseOrder.approvedAt,
            by: receipt.purchaseOrder.approvedBy,
          } : null,
        },
      },
    };

    return NextResponse.json(enrichedReceipt);

  } catch (error) {
    console.error('Error fetching receipt details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
